// /*-- icon --*/

.icon
  &--setting
    display: inline-block
    width: 18px
    height: 18px
    background: url($path+'icon-setting.svg')
    background-size: 100% auto
    background-position: center top
    background-repeat: no-repeat
    margin-right: 5px
    @media (max-width:$bk-mbxs)
      width: 12px
      height: 12px
      margin-right: 3px
  &--recording
    display: inline-block
    width: 18px
    height: 18px
    background: url($path+'icon-recording.svg')
    background-size: 100% auto
    background-position: center top
    background-repeat: no-repeat
    margin-right: 5px
    @media (max-width:$bk-mbxs)
      width: 12px
      height: 12px
      margin-right: 3px
  &--sort
    display: block
    width: 20px
    height: 17px
    background: url($path+'icon-sort.svg')
    background-size: 100% auto
    background-position: center top
    background-repeat: no-repeat
  &--remove
    display: inline-block
    width: 16px
    height: 19px
    background: url($path+'icon-remove.svg')
    background-size: 100% auto
    background-position: center top
    background-repeat: no-repeat
  &--list
    display: inline-block
    width: 19px
    height: 17px
    background: url($path+'icon-list.svg')
    background-size: 100% auto
    background-position: center top
    background-repeat: no-repeat
  &--refresh
    display: inline-block
    width: 17px
    height: 16px
    background: url($path+'icon-refresh.svg')
    background-size: 100% auto
    background-position: center top
    background-repeat: no-repeat
  &--switch
    display: inline-block
    width: 21px
    height: 20px
    background: url($path+'icon-switch.svg')
    background-size: 100% auto
    background-position: center top
    background-repeat: no-repeat
  &--random
    display: inline-block
    width: 19px
    height: 17px
    background: url($path+'icon-random.svg')
    background-size: 100% auto
    background-position: center top
    background-repeat: no-repeat
  &--excel
    display: inline-block
    width: 16px
    height: 21px
    background: url($path+'icon-excel.svg')
    background-size: 100% auto
    background-position: center top
    background-repeat: no-repeat
    @media (max-width:$bk-mblg)
      width: 13px
      height: 17px
  &--lottery
    display: inline-block
    width: 36px
    height: 38px
    background: url($path+'icon-lottery.svg')
    background-size: 100% auto
    background-position: center top
    background-repeat: no-repeat
    margin-right: 14px
    @media (max-width:$bk-mblg)
      width: 26px
      height: 28px
      margin-right: 6px
  &--add
    display: inline-block
    width: 16px
    height: 16px
    position: relative
    &::before
      content: ""
      display: block
      width: 16px
      height: 2px
      background: $color-orange
      position: absolute
      top: calc( 50% - 1px )
    &::after
      content: ""
      display: block
      width: 16px
      height: 2px
      background: $color-orange
      position: absolute
      top: calc( 50% - 1px )
      transform: rotate(90deg)

