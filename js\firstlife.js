$(function() {
    // 當 ID 為 'add-award-btn' 的「新增獎項」按鈕被點擊時
    $('#add-award-btn').on('click', function(e) {
        e.preventDefault(); // 防止按鈕的預設行為 (例如：表單提交)

        // 找到獎項列表的容器
        var awardsBlock = $('#awards-block');
        
        // 複製容器中的第一個獎項列，把它當作新獎項的模板
        // .find() 會從 awardsBlock 內部尋找元素
        var newRow = awardsBlock.find('.awards__row:first').clone();

        // 如果找不到任何可以複製的列 (例如全部被清空了)，就直接結束，避免錯誤
        if (newRow.length === 0) {
            console.error('找不到可複製的獎項模板。');
            return; 
        }

        // 清空新獎項列中的輸入欄位值
        newRow.find('input[type="text"]').val('');
        // 將數量輸入框重設為預設值
        newRow.find('input[type="number"]').val('1'); 

        // 將處理好的新獎項列，附加到列表容器的最後
        awardsBlock.append(newRow);
        
        // 延遲執行儲存，但不同步（避免覆蓋新增的行）
        setTimeout(function() {
            // 儲存到 IndexedDB（如果存在 saveAwardsToStorage 函數）
            if (typeof saveAwardsToStorage === 'function') {
                saveAwardsToStorage();
            }
        }, 100);
    });
});
$(document).ready(function () {
  'use strict';
  
  if ( window.self === window.top ) {
    console.log('self === top');
  }
  else {
    console.error('error! clickjacking!');
    var content = "If you see this page,is under Clickjacking security attack";
    document.getElementsByTagName('body')[0].innerHTML = content;
  }

  
  $('.js-switch').on('click', function (e) {
    // alert($('.drawpool__wrap.active').index());
    var currentIndex = $('.drawpool__wrap.active').index();
    
    $('.drawpool__wrap').removeClass('active');

    if(currentIndex == 0){
      $('.drawpool__wrap').eq(1).addClass('active');
    }else{
      $('.drawpool__wrap').eq(0).addClass('active');
    }
  });

  //抽獎設定
  $('.js-setting').on('click', function () {
    lity('#pop-setting');
  });

});

//開始抽獎
$(function () {
  // 統一的抽獎按鈕處理
  $('.js-lottery').on('click', function () {
    // 檢查重複中獎規則
    const winRule1 = $('#radio-pop-2-1').is(':checked');
    const winRule2 = $('#radio-pop-2-2').is(':checked');
    if (!winRule1 && !winRule2) {
      alert('請選擇重複中獎規則');
      return;
    }

    // 檢查是否有填入獎項內容
    const prizes = getPrizeList();
    console.log(prizes);
    if (prizes.length === 0) {
      alert('請先新增獎項');
      return;
    }

    // 檢查是否有填入參加者名單
    const participants = $('#participants').val();
    if (participants.length === 0) {
      alert('請先新增參加者名單');
      return;
    }

    // 檢查動畫設定
    if (window.currentSettings && window.currentSettings.skipAnimation === false) {
      // 開啟動畫：顯示動畫彈窗
      var instance = lity('#pop-lottery');
      launchFireworksThenClose(instance, 3000);
    } else {
      // 關閉動畫：直接執行抽獎
      performDraw();
    }
  });

  function launchFireworksThenClose(instance, duration) {
    var animationEnd = Date.now() + duration;
    var defaults = {
      startVelocity: 30,
      spread: 360,
      ticks: 60,
      zIndex: 9999
    };

    function randomInRange(min, max) {
      return Math.random() * (max - min) + min;
    }

    var interval = setInterval(function () {
      var timeLeft = animationEnd - Date.now();

      if (timeLeft <= 0) {
        clearInterval(interval);
      }

      var particleCount = 50 * (timeLeft / duration);
      confetti({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 }
      });
      confetti({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 }
      });
    }, 250);

    setTimeout(() => {
      performDraw();
      instance.close();
    }, duration);
  }

});
