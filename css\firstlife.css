@charset "UTF-8";
html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, font, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, ar, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  font-size: 100%;
}

@font-face {
  font-family: "Noto Sans TC";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../vendor/fonts/NotoSansTC-Regular.ttf") format("truetype");
}
@font-face {
  font-family: "Noto Sans TC";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../vendor/fonts/NotoSansTC-Medium.ttf") format("truetype");
}
@font-face {
  font-family: "Noto Sans TC";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../vendor/fonts/NotoSansTC-Bold.ttf") format("truetype");
}
body {
  font-family: "Noto Sans TC", "微軟正黑體", sans-serif;
  color: #333333;
  font-weight: 400;
  line-height: 1.2;
  margin: 0 auto;
  position: relative;
  overflow-x: hidden;
  background: url("../images/bg_02.jpg");
  background-size: auto;
  background-position: center top;
  background-repeat: repeat-y;
}
@media (min-width: 1922px) {
  body {
    background-size: 100% auto;
  }
}

select, button, textarea, input {
  font-family: "Noto Sans TC", "微軟正黑體", sans-serif;
  outline: none;
}

* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  -webkit-text-size-adjust: none;
}

table {
  border-collapse: collapse;
}

a, a:focus {
  cursor: pointer;
  text-decoration: none;
  -webkit-transition: color 300ms;
  transition: color 300ms;
}

a:active {
  outline: none;
}

img {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  max-width: 100%;
}

.container {
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
@media (min-width: 768px) {
  .container {
    width: 650px;
    padding-right: 0px;
    padding-left: 0px;
  }
}
@media (min-width: 992px) {
  .container {
    width: 900px;
  }
}
@media (min-width: 1200px) {
  .container {
    width: 1050px;
  }
}
@media (min-width: 1400px) {
  .container {
    width: 1350px;
  }
}

.container--lg {
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
@media (min-width: 768px) {
  .container--lg {
    width: 650px;
    padding-right: 0px;
    padding-left: 0px;
  }
}
@media (min-width: 992px) {
  .container--lg {
    width: 900px;
  }
}
@media (min-width: 1200px) {
  .container--lg {
    width: 1050px;
  }
}
@media (min-width: 1400px) {
  .container--lg {
    width: 1350px;
  }
}
@media (min-width: 1700px) {
  .container--lg {
    width: 1680px;
  }
}

.icon--setting {
  display: inline-block;
  width: 18px;
  height: 18px;
  background: url("../images/icon-setting.svg");
  background-size: 100% auto;
  background-position: center top;
  background-repeat: no-repeat;
  margin-right: 5px;
}
@media (max-width: 374px) {
  .icon--setting {
    width: 12px;
    height: 12px;
    margin-right: 3px;
  }
}
.icon--recording {
  display: inline-block;
  width: 18px;
  height: 18px;
  background: url("../images/icon-recording.svg");
  background-size: 100% auto;
  background-position: center top;
  background-repeat: no-repeat;
  margin-right: 5px;
}
@media (max-width: 374px) {
  .icon--recording {
    width: 12px;
    height: 12px;
    margin-right: 3px;
  }
}
.icon--sort {
  display: block;
  width: 20px;
  height: 17px;
  background: url("../images/icon-sort.svg");
  background-size: 100% auto;
  background-position: center top;
  background-repeat: no-repeat;
}
.icon--remove {
  display: inline-block;
  width: 16px;
  height: 19px;
  background: url("../images/icon-remove.svg");
  background-size: 100% auto;
  background-position: center top;
  background-repeat: no-repeat;
}
.icon--list {
  display: inline-block;
  width: 19px;
  height: 17px;
  background: url("../images/icon-list.svg");
  background-size: 100% auto;
  background-position: center top;
  background-repeat: no-repeat;
}
.icon--refresh {
  display: inline-block;
  width: 17px;
  height: 16px;
  background: url("../images/icon-refresh.svg");
  background-size: 100% auto;
  background-position: center top;
  background-repeat: no-repeat;
}
.icon--switch {
  display: inline-block;
  width: 21px;
  height: 20px;
  background: url("../images/icon-switch.svg");
  background-size: 100% auto;
  background-position: center top;
  background-repeat: no-repeat;
}
.icon--random {
  display: inline-block;
  width: 19px;
  height: 17px;
  background: url("../images/icon-random.svg");
  background-size: 100% auto;
  background-position: center top;
  background-repeat: no-repeat;
}
.icon--excel {
  display: inline-block;
  width: 16px;
  height: 21px;
  background: url("../images/icon-excel.svg");
  background-size: 100% auto;
  background-position: center top;
  background-repeat: no-repeat;
}
@media (max-width: 480px) {
  .icon--excel {
    width: 13px;
    height: 17px;
  }
}
.icon--lottery {
  display: inline-block;
  width: 36px;
  height: 38px;
  background: url("../images/icon-lottery.svg");
  background-size: 100% auto;
  background-position: center top;
  background-repeat: no-repeat;
  margin-right: 14px;
}
@media (max-width: 480px) {
  .icon--lottery {
    width: 26px;
    height: 28px;
    margin-right: 6px;
  }
}
.icon--add {
  display: inline-block;
  width: 16px;
  height: 16px;
  position: relative;
}
.icon--add::before {
  content: "";
  display: block;
  width: 16px;
  height: 2px;
  background: #ff4b0f;
  position: absolute;
  top: calc(50% - 1px);
}
.icon--add::after {
  content: "";
  display: block;
  width: 16px;
  height: 2px;
  background: #ff4b0f;
  position: absolute;
  top: calc(50% - 1px);
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}

.btn {
  color: #ffffff;
  font-weight: 500;
  font-size: 1rem;
  letter-spacing: 0.05em;
  text-align: center;
  height: 48px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  -webkit-transition: opacity 0.3s ease;
  transition: opacity 0.3s ease;
  -webkit-box-shadow: 0 0 7px rgba(141, 141, 141, 0.38);
          box-shadow: 0 0 7px rgba(141, 141, 141, 0.38);
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
@media (max-width: 480px) {
  .btn {
    letter-spacing: 0;
  }
}
@media (max-width: 374px) {
  .btn {
    font-size: 0.875rem;
    line-height: 40px;
  }
}
@media (hover: hover) {
  .btn:hover {
    opacity: 0.8;
  }
}
.btn:disabled, .btn [disabled] {
  background: #a6a6a6;
  cursor: not-allowed;
}
@media (hover: hover) {
  .btn:disabled:hover, .btn [disabled]:hover {
    opacity: 1;
  }
}
.btn--lg {
  width: 430px;
  height: 80px;
  font-size: 1.375rem;
}
@media (max-width: 480px) {
  .btn--lg {
    width: 100%;
    font-size: 1.125rem;
    height: 60px;
  }
}
.btn--md {
  width: 162px;
  padding: 0;
}
@media (max-width: 767px) {
  .btn--md {
    width: 120px;
  }
}
@media (max-width: 374px) {
  .btn--md {
    width: 90px;
  }
}
.btn--md2 {
  width: 124px;
}
@media (max-width: 767px) {
  .btn--md2 {
    width: 5.5em;
  }
}
.btn--md3 {
  width: 175px;
}
@media (max-width: 767px) {
  .btn--md3 {
    width: 6.5em;
  }
}
.btn--sm {
  width: 90px;
}
@media (max-width: 767px) {
  .btn--sm {
    width: 74px;
  }
}
@media (max-width: 374px) {
  .btn--sm {
    width: 64px;
  }
}
.btn--xs {
  width: 36px;
  height: 36px;
  padding: 0;
}
@media (max-width: 374px) {
  .btn--xs {
    width: 10vw;
    height: 10vw;
  }
}
.btn--green {
  background: #0b9e2c;
}
.btn--orange {
  background: #ff4b0f;
}
.btn--gray {
  background: #757575;
}
.btn--excel {
  font-size: 0.875rem;
  font-weight: 500;
  color: #0b9e2c;
  letter-spacing: 0;
  border: 1px solid #0b9e2c;
  background: transparent;
  width: 118px;
  height: 36px;
  padding: 0;
}
@media (max-width: 374px) {
  .btn--excel {
    width: 10vw;
    height: 10vw;
  }
}
.btn--excel [class^=icon] {
  margin-right: 6px;
}
@media (max-width: 374px) {
  .btn--excel [class^=icon] {
    margin-right: 0;
  }
}
.btn--add {
  font-size: 1rem;
  font-weight: 500;
  color: #ff4b0f;
  letter-spacing: 0.05em;
  border: 2px dotted #ff4b0f;
  background: transparent;
  width: 100%;
  height: 55px;
}
.btn--add [class^=icon] {
  margin-right: 8px;
}
@media (hover: hover) {
  .btn--add:hover {
    color: #ffffff;
    background: #ff4b0f;
  }
  .btn--add:hover [class^=icon]::before, .btn--add:hover [class^=icon]::after {
    background: #ffffff;
  }
}

.form {
  padding-top: 116px;
}
@media (max-width: 767px) {
  .form {
    padding-top: 60px;
    padding-bottom: 0;
  }
}
.form__wrap {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  width: calc(100% + 64px);
  margin-left: -32px;
  margin-right: -32px;
}
@media (max-width: 1699px) {
  .form__wrap {
    width: calc(100% + 30px);
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media (max-width: 1399px) {
  .form__wrap {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}
.form__wrap-item {
  background: #ffffff;
  border-radius: 30px;
  -webkit-box-shadow: 5px 10px 35px rgba(141, 141, 141, 0.25);
          box-shadow: 5px 10px 35px rgba(141, 141, 141, 0.25);
  padding: 30px 44px 35px;
  width: calc(50% - 64px);
  margin-left: 32px;
  margin-right: 32px;
}
@media (max-width: 1699px) {
  .form__wrap-item {
    width: calc(50% - 30px);
    margin-left: 15px;
    margin-right: 15px;
  }
}
@media (max-width: 1399px) {
  .form__wrap-item {
    padding: 30px 15px;
  }
}
@media (max-width: 1199px) {
  .form__wrap-item {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    margin-top: 30px;
  }
  .form__wrap-item:first-child {
    margin-top: 0;
  }
}
.form__wrap--colspan {
  width: 100%;
  margin-left: 0;
  margin-right: 0;
}
.form__wrap--colspan .form__wrap-item {
  width: 100%;
  margin-left: 0;
  margin-right: 0;
}
.form__head {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 100%;
}
@media (max-width: 1399px) {
  .form__head {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}
.form__footer {
  margin-top: 50px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  width: 100%;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
@media (max-width: 1199px) {
  .form__footer {
    margin-top: 30px;
  }
}
.form__footer .btn {
  margin-left: 36px;
}
@media (max-width: 991px) {
  .form__footer .btn {
    margin-left: 20px;
  }
}
@media (max-width: 767px) {
  .form__footer .btn {
    margin-left: 10px;
  }
}
@media (max-width: 374px) {
  .form__footer .btn {
    width: 124px;
    margin-left: 5px;
  }
}
.form__footer .btn:first-child {
  margin-left: 0;
}
.form__btn2 {
  margin-top: 80px;
  text-align: center;
}
@media (max-width: 1199px) {
  .form__btn2 {
    margin-top: 30px;
  }
}
.form__title {
  font-size: 1.625rem;
  font-weight: 700;
  position: relative;
  color: #383838;
  letter-spacing: 0.05em;
  position: relative;
  padding-left: 125px;
}
@media (max-width: 1699px) {
  .form__title {
    padding-left: 80px;
  }
}
@media (max-width: 1399px) {
  .form__title {
    padding-left: 40px;
    text-align: center;
    width: 100%;
  }
}
@media (max-width: 480px) {
  .form__title {
    font-size: 1.375rem;
  }
}
.form__title-icon {
  position: absolute;
  top: -95px;
  left: -40px;
  width: 162px;
  pointer-events: none;
}
@media (max-width: 1699px) {
  .form__title-icon {
    top: -65px;
    width: 120px;
  }
}
@media (max-width: 1399px) {
  .form__title-icon {
    top: -25px;
    left: calc(50% - 105px);
    width: 70px;
  }
}
@media (max-width: 480px) {
  .form__title-icon {
    top: -20px;
    left: calc(50% - 90px);
    width: 60px;
  }
}
.form__btn {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
@media (max-width: 1399px) {
  .form__btn {
    width: 100%;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    margin-top: 20px;
  }
}
.form__btn .btn {
  margin-left: 8px;
}
@media (max-width: 480px) {
  .form__btn .btn {
    margin-left: 4px;
  }
}
.form__btn .btn:first-child {
  margin-left: 0;
}
.form__input {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  position: relative;
  z-index: 1;
  padding: 0 18px;
  font-size: 1rem;
  font-weight: 500;
  color: #383838;
  letter-spacing: 0.05em;
  background: #e6e6e6;
  border: none;
  border-radius: 8px;
  -webkit-box-shadow: 0 0 7px rgba(0, 0, 0, 0.14) inset;
          box-shadow: 0 0 7px rgba(0, 0, 0, 0.14) inset;
  height: 55px;
  width: 100%;
}
.form__input:disabled, .form__input [disabled] {
  -webkit-box-shadow: none;
          box-shadow: none;
  background: #eeeeee;
  color: #cecece;
  cursor: not-allowed;
  -webkit-text-fill-color: #cecece;
  opacity: 1;
}
.form__input::-webkit-input-placeholder {
  color: #a6a6a6;
}
.form__input::-moz-placeholder {
  color: #a6a6a6;
}
.form__input:-ms-input-placeholder {
  color: #a6a6a6;
}
.form__input::-ms-input-placeholder {
  color: #a6a6a6;
}
.form__input::placeholder {
  color: #a6a6a6;
}
.form__selectbox {
  position: relative;
  width: 100%;
  border-radius: 8px;
  background: #e6e6e6;
}
.form__selectbox::before {
  content: "";
  display: block;
  width: 17px;
  height: 10px;
  position: absolute;
  top: calc(50% - 5px);
  right: 15px;
  background: url("../images/icon-arrow.svg");
  background-size: 100% auto;
  background-position: center top;
  background-repeat: no-repeat;
}
.form__select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding-top: 0;
  padding-bottom: 0;
  padding-right: 40px;
  padding-left: 18px;
  font-size: 1rem;
  font-weight: 500;
  color: #383838;
  background: transparent;
  border: none;
  border-radius: 8px;
  -webkit-box-shadow: 0 0 7px rgba(0, 0, 0, 0.14) inset;
          box-shadow: 0 0 7px rgba(0, 0, 0, 0.14) inset;
  width: 100%;
  height: 55px;
  position: relative;
  z-index: 1;
}
.form__select::-ms-expand {
  display: none;
}
.form__select:disabled, .form__select [disabled] {
  -webkit-box-shadow: none;
          box-shadow: none;
  background: #eeeeee;
  color: #cecece;
  cursor: not-allowed;
  -webkit-text-fill-color: #cecece;
  opacity: 1;
}
.form__select:has(option:checked[value=""]) {
  color: #a6a6a6;
}
.form__select option:first-child {
  color: #a6a6a6;
}
.form__select option:not(:first-child) {
  color: #383838;
}
.form__textarea {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  position: relative;
  z-index: 1;
  padding: 15px 18px;
  font-size: 1rem;
  font-weight: 500;
  color: #383838;
  letter-spacing: 0.05em;
  line-height: 1.6;
  background: #e6e6e6;
  border: none;
  border-radius: 8px;
  -webkit-box-shadow: 0 0 7px rgba(0, 0, 0, 0.14) inset;
          box-shadow: 0 0 7px rgba(0, 0, 0, 0.14) inset;
  height: 320px;
  width: 100%;
  resize: none;
}
.form__textarea:disabled, .form__textarea [disabled] {
  -webkit-box-shadow: none;
          box-shadow: none;
  background: #eeeeee;
  color: #cecece;
  cursor: not-allowed;
  -webkit-text-fill-color: #cecece;
  opacity: 1;
}
.form__textarea::-webkit-input-placeholder {
  color: #a6a6a6;
}
.form__textarea::-moz-placeholder {
  color: #a6a6a6;
}
.form__textarea:-ms-input-placeholder {
  color: #a6a6a6;
}
.form__textarea::-ms-input-placeholder {
  color: #a6a6a6;
}
.form__textarea::placeholder {
  color: #a6a6a6;
}
.form__label {
  display: inline-block;
  padding-right: 14px;
  font-size: 1.25rem;
  font-weight: 700;
  color: #383838;
  min-width: 116px;
}
@media (max-width: 374px) {
  .form__label {
    min-width: 100px;
  }
}
.form__radioblock {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  width: 100%;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.form__radioblock .form__radiobox {
  width: 238px;
}
@media (max-width: 991px) {
  .form__radioblock .form__radiobox {
    width: 200px;
  }
}
@media (max-width: 767px) {
  .form__radioblock .form__radiobox {
    width: 100%;
    margin-top: 10px;
  }
  .form__radioblock .form__radiobox:first-child {
    margin-top: 0;
  }
}
.form__radiobox .form__radio {
  display: none;
}
.form__radio:disabled + .form__radiolabel, .form__radio[disabled] + .form__radiolabel {
  cursor: not-allowed;
}
.form__radio:disabled + .form__radiolabel::before, .form__radio[disabled] + .form__radiolabel::before {
  background: #eeeeee;
}
.form__radio:disabled:checked + .form__radiolabel::after, .form__radio[disabled][checked] + .form__radiolabel::after {
  background: #cecece;
}
.form__radio:checked + .form__radiolabel::after {
  content: "";
  display: block;
  width: 15px;
  height: 15px;
  position: absolute;
  left: 5px;
  top: 5px;
  background: #ff4b0f;
  border-radius: 100%;
}
.form__radiolabel {
  cursor: pointer;
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 1.125rem;
  font-weight: 700;
  color: #717171;
  text-align: left;
  line-height: 1;
}
.form__radiolabel::before {
  content: "";
  display: block;
  width: 25px;
  min-width: 25px;
  height: 25px;
  background-color: #f4f4f4;
  border: 1px solid #d4d4d4;
  border-radius: 100%;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
.form__radiolabel span {
  margin-left: 7px;
}
.form__radiolabel a {
  color: #333333;
  text-decoration: underline;
}

.pop__wrap--download {
  position: relative;
  z-index: 2;
  width: 510px;
  background: #ffffff;
  border-radius: 30px;
  padding: 35px 45px;
}
@media (max-width: 767px) {
  .pop__wrap--download {
    width: 90vw;
  }
}
@media (max-width: 480px) {
  .pop__wrap--download {
    padding-left: 15px;
    padding-right: 15px;
  }
}
.pop__wrap--download .pop__body {
  padding-top: 45px;
  padding-bottom: 45px;
}
.pop__wrap--download .pop__btn {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  width: 100%;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
@media (max-width: 767px) {
  .pop__wrap--download .pop__btn {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    width: calc(100% + 10px);
    margin-left: -5px;
    margin-right: -5px;
  }
}
@media (max-width: 374px) {
  .pop__wrap--download .pop__btn {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}
@media (max-width: 767px) {
  .pop__wrap--download .btn {
    margin-left: 5px;
    margin-right: 5px;
    height: 66px;
    line-height: 1.4;
  }
}
.pop__wrap--setting {
  position: relative;
  z-index: 2;
  width: 732px;
  background: #ffffff;
  border-radius: 30px;
  padding: 35px 45px;
}
@media (max-width: 767px) {
  .pop__wrap--setting {
    width: 90vw;
  }
}
@media (max-width: 480px) {
  .pop__wrap--setting {
    padding-left: 15px;
    padding-right: 15px;
  }
}
.pop__wrap--setting .pop__body {
  padding-top: 40px;
}
.pop__wrap--setting .pop__btn {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: calc(100% + 20px);
  margin-left: -10px;
  margin-right: -10px;
  margin-top: 45px;
}
@media (max-width: 767px) {
  .pop__wrap--setting .pop__btn {
    width: calc(100% + 10px);
    margin-left: -5px;
    margin-right: -5px;
    margin-top: 30px;
  }
}
@media (max-width: 374px) {
  .pop__wrap--setting .pop__btn {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}
.pop__wrap--setting .pop__form-item {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  width: 100%;
  padding-top: 26px;
  padding-bottom: 26px;
  border-top: 1px solid #d8d8d8;
}
.pop__wrap--setting .pop__form-item:first-child {
  padding-top: 0;
  border-top: none;
}
.pop__wrap--setting .pop__form-item:last-child {
  padding-bottom: 0;
}
.pop__wrap--setting .btn {
  margin-left: 10px;
  margin-right: 10px;
}
@media (max-width: 767px) {
  .pop__wrap--setting .btn {
    margin-left: 5px;
    margin-right: 5px;
  }
}
.pop__title {
  font-size: 1.625rem;
  font-weight: 700;
  color: #383838;
  letter-spacing: 0.05em;
}
.pop__lottery {
  width: 502px;
  height: 470px;
  background: url("../images/pop-lottery.png");
  background-size: 100% auto;
  background-position: center top;
  background-repeat: no-repeat;
  position: relative;
  -webkit-animation: ani-pop-img 1s steps(2) infinite;
          animation: ani-pop-img 1s steps(2) infinite;
}
@media (max-width: 991px) {
  .pop__lottery {
    width: 266px;
    height: 250px;
    -webkit-animation-name: ani-pop-img-tb;
            animation-name: ani-pop-img-tb;
  }
}
@media (max-width: 374px) {
  .pop__lottery {
    width: 213px;
    height: 200px;
    -webkit-animation-name: ani-pop-img-mbxs;
            animation-name: ani-pop-img-mbxs;
  }
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1020;
  width: 100%;
  background: #fff;
}
@media (min-width: 768px) {
  .header .container {
    width: 100%;
  }
}
@media (max-width: 991px) {
  .header--active .header__toggle {
    z-index: 1040;
  }
  .header--active .header__toggle-item:first-child {
    top: 14px;
    -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
  }
  .header--active .header__toggle-item:nth-child(2) {
    display: none;
  }
  .header--active .header__toggle-item:last-child {
    top: 14px;
    -webkit-transform: rotate(-45deg);
            transform: rotate(-45deg);
  }
}
@media (max-width: 991px) {
  .header__head {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    width: 100%;
  }
}
.header__block {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  padding: 18px 45px 16px 38px;
}
@media (max-width: 1399px) {
  .header__block {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media (max-width: 767px) {
  .header__block {
    padding: 5px 0;
  }
}
.header__logo {
  display: block;
  width: 194px;
}
@media (max-width: 480px) {
  .header__logo {
    width: 130px;
  }
}
@media (max-width: 374px) {
  .header__logo {
    width: 38vw;
  }
}
.header__logo img {
  width: 100%;
  max-width: 100%;
  display: block;
}
.header__btn {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.header__btn .btn {
  margin-left: 20px;
}
@media (max-width: 767px) {
  .header__btn .btn {
    margin-left: 10px;
  }
}
@media (max-width: 374px) {
  .header__btn .btn {
    margin-left: 5px;
  }
}
.header__btn .btn:first-child {
  margin-left: 0;
}

.footer {
  font-size: 1rem;
  color: #848484;
  letter-spacing: 0.05em;
  text-align: center;
  padding-top: 35px;
  padding-bottom: 35px;
}

.kv {
  position: relative;
  overflow: hidden;
  background: url("../images/bg_01.png"), url("../images/kv-bg.jpg");
  background-size: auto, cover;
  background-position: center bottom, center top;
  background-repeat: no-repeat, no-repeat;
}
@media (min-width: 1922px) {
  .kv {
    background-size: 100% auto, 100% auto;
  }
}
@media (max-width: 1399px) {
  .kv {
    background-size: 1400px auto, 1400px auto;
  }
}
@media (max-width: 1199px) {
  .kv {
    background-size: 1200px auto, 1200px auto;
  }
}
@media (max-width: 991px) {
  .kv {
    background-size: 992px auto, 992px auto;
    margin-top: 40px;
  }
}
@media (max-width: 849px) {
  .kv {
    background-size: 850px auto, 850px auto;
  }
}
@media (max-width: 767px) {
  .kv {
    background: url("../images/bg_01.png"), url("../images/kv-bg-sm.jpg");
    background-size: 100% auto, 100% auto;
    background-position: center bottom, center top;
    background-repeat: no-repeat, no-repeat;
    margin-top: 58px;
  }
}
@media (max-width: 374px) {
  .kv {
    margin-top: 50px;
  }
}
.kv .container--lg {
  padding-left: 35px;
  height: 630px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding-bottom: 70px;
}
@media (min-width: 1922px) {
  .kv .container--lg {
    height: 32vw;
  }
}
@media (max-width: 1699px) {
  .kv .container--lg {
    padding-left: 0;
    padding-bottom: 50px;
    height: 540px;
  }
}
@media (max-width: 1399px) {
  .kv .container--lg {
    height: 420px;
    padding-bottom: 10px;
  }
}
@media (max-width: 1199px) {
  .kv .container--lg {
    height: 380px;
    padding-bottom: 0;
  }
}
@media (max-width: 991px) {
  .kv .container--lg {
    height: 320px;
  }
}
@media (max-width: 849px) {
  .kv .container--lg {
    height: 280px;
  }
}
@media (max-width: 767px) {
  .kv .container--lg {
    height: 106vw;
    padding-left: 15px;
    padding-top: 6vw;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}
.kv__item {
  width: 10em;
  font-size: 4rem;
  font-weight: 700;
  color: #ffffff;
  line-height: 1.24em;
  display: -webkit-box;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  max-height: 2.48em;
  text-shadow: 0 0 14px rgba(196, 128, 8, 0.56);
}
@media (max-width: 1699px) {
  .kv__item {
    font-size: 3.375rem;
    margin-left: -10px;
  }
}
@media (max-width: 1399px) {
  .kv__item {
    font-size: 2.75rem;
    margin-left: -60px;
  }
}
@media (max-width: 1199px) {
  .kv__item {
    font-size: 2.375rem;
    position: absolute;
    top: calc(50% - 60px);
    left: calc(50vw - 420px);
    text-shadow: 0 0 10px rgba(196, 128, 8, 0.56);
  }
}
@media (max-width: 991px) {
  .kv__item {
    font-size: 2rem;
    top: calc(50% - 55px);
    left: calc(50vw - 350px);
  }
}
@media (max-width: 849px) {
  .kv__item {
    font-size: 1.75rem;
    top: calc(50% - 50px);
    left: calc(50vw - 290px);
  }
}
@media (max-width: 767px) {
  .kv__item {
    position: relative;
    top: auto;
    left: auto;
    font-size: 9vw;
    margin-left: auto;
    margin-right: auto;
  }
}

.awards {
  position: relative;
  padding-top: 25px;
}
@media (max-width: 767px) {
  .awards {
    padding-right: 0;
  }
}
.awards__wrap {
  display: none;
}
.awards__wrap.active {
  display: block;
}
.awards__block-wrap {
  max-height: 190px;
  overflow-x: hidden;
  overflow-y: auto;
}
@media (max-width: 767px) {
  .awards__block-wrap {
    max-height: 178px;
  }
}
.awards__form {
  width: 100%;
}
.awards__form .form__textarea {
  min-height: 190px;
  resize: vertical;
}
@media (max-width: 767px) {
  .awards__form .form__textarea {
    min-height: 178px;
  }
}
.awards__row {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  width: 100%;
  margin-top: 12px;
}
@media (max-width: 767px) {
  .awards__row {
    margin-top: 6px;
  }
}
.awards__row:first-child {
  margin-top: 0;
}
.awards__col:nth-child(1) {
  width: calc(100% - 126px);
}
@media (max-width: 767px) {
  .awards__col:nth-child(1) {
    width: calc(100% - 106px);
  }
}
.awards__col:nth-child(2) {
  width: 110px;
  margin-left: 16px;
}
@media (max-width: 767px) {
  .awards__col:nth-child(2) {
    margin-left: 6px;
    width: 100px;
  }
}
.awards__add {
  margin-top: 16px;
}
.awards__btn {
  margin-top: 25px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  width: 100%;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.awards__btn .btn {
  margin-left: 20px;
}
@media (max-width: 767px) {
  .awards__btn .btn {
    margin-left: 10px;
  }
}
@media (max-width: 374px) {
  .awards__btn .btn {
    margin-left: 5px;
  }
}
.awards__btn .btn:first-child {
  margin-left: 0;
}

.drawpool {
  margin-top: 34px;
}
@media (max-width: 1199px) {
  .drawpool {
    margin-top: 25px;
  }
}
.drawpool__wrap {
  display: none;
}
.drawpool__wrap.active {
  display: block;
}
.drawpool__block {
  overflow: auto;
  max-height: 325px;
  width: 100%;
}
.drawpool thead tr {
  background: #f47051;
}
.drawpool thead th {
  color: #ffffff;
  font-size: 1.125rem;
  font-weight: 700;
  color: #ffffff;
  border: 3px solid #ffffff;
  border-top: none;
}
.drawpool tbody tr {
  background: #e6e6e6;
}
.drawpool tbody td {
  border: 3px solid #ffffff;
}
.drawpool th, .drawpool td {
  padding: 14px;
  white-space: nowrap;
  text-align: center;
}
.drawpool th:first-child, .drawpool td:first-child {
  min-width: 208px;
  border-left: none;
}
@media (max-width: 480px) {
  .drawpool th:first-child, .drawpool td:first-child {
    min-width: 130px;
  }
}
.drawpool th:nth-child(2), .drawpool td:nth-child(2) {
  min-width: 198px;
}
@media (max-width: 480px) {
  .drawpool th:nth-child(2), .drawpool td:nth-child(2) {
    min-width: 150px;
  }
}
.drawpool th:nth-child(3), .drawpool td:nth-child(3) {
  min-width: 304px;
}
@media (max-width: 480px) {
  .drawpool th:nth-child(3), .drawpool td:nth-child(3) {
    min-width: 280px;
  }
}

.result {
  margin-top: 75px;
  display: none;
}
.result.active {
  display: block;
}
.result .form__body {
  margin-top: 55px;
}
@media (max-width: 991px) {
  .result .form__body {
    margin-top: 30px;
  }
}
.result__wrap {
  margin-top: 30px;
}
.result__wrap:first-child {
  margin-top: 0;
}
.result__wrap-item {
  position: relative;
}
.result__title {
  font-size: 1.25rem;
  font-weight: 500;
  color: #000000;
  letter-spacing: 0.05em;
  position: relative;
  padding-left: 44px;
  margin-bottom: 20px;
}
@media (max-width: 767px) {
  .result__title {
    padding-left: 20px;
    font-size: 1.125rem;
  }
}
.result__title::before {
  content: "";
  display: block;
  width: 8px;
  height: 8px;
  background: #ff4b0f;
  border-radius: 10em;
  position: absolute;
  top: 10px;
  left: 16px;
}
@media (max-width: 767px) {
  .result__title::before {
    top: 8px;
    left: 0;
    width: 6px;
    height: 6px;
  }
}
.result__block {
  overflow: auto;
  max-height: 576px;
  width: 100%;
}
.result thead tr {
  background: #f47051;
}
.result thead th {
  color: #ffffff;
  font-size: 1.125rem;
  font-weight: 700;
  color: #ffffff;
  border: 3px solid #ffffff;
  border-top: none;
}
.result tbody tr {
  background: #e6e6e6;
}
.result tbody td {
  border: 3px solid #ffffff;
}
.result th, .result td {
  padding: 14px;
  white-space: nowrap;
  text-align: center;
  min-width: 260px;
}
.result th:first-child, .result td:first-child {
  min-width: 368px;
  border-left: none;
}
@media (max-width: 1699px) {
  .result th:first-child, .result td:first-child {
    min-width: 350px;
  }
}
@media (max-width: 1399px) {
  .result th:first-child, .result td:first-child {
    min-width: 290px;
  }
}
@media (max-width: 1199px) {
  .result th:first-child, .result td:first-child {
    min-width: 208px;
  }
}
@media (max-width: 480px) {
  .result th:first-child, .result td:first-child {
    min-width: 130px;
  }
}
.result th:nth-child(2), .result td:nth-child(2) {
  min-width: 514px;
}
@media (max-width: 1699px) {
  .result th:nth-child(2), .result td:nth-child(2) {
    min-width: 350px;
  }
}
@media (max-width: 1399px) {
  .result th:nth-child(2), .result td:nth-child(2) {
    min-width: 290px;
  }
}
@media (max-width: 1199px) {
  .result th:nth-child(2), .result td:nth-child(2) {
    min-width: 198px;
  }
}
@media (max-width: 480px) {
  .result th:nth-child(2), .result td:nth-child(2) {
    min-width: 150px;
  }
}
.result th:nth-child(3), .result td:nth-child(3) {
  min-width: 700px;
}
@media (max-width: 1699px) {
  .result th:nth-child(3), .result td:nth-child(3) {
    min-width: 540px;
  }
}
@media (max-width: 1399px) {
  .result th:nth-child(3), .result td:nth-child(3) {
    min-width: 416px;
  }
}
@media (max-width: 1199px) {
  .result th:nth-child(3), .result td:nth-child(3) {
    min-width: 304px;
  }
}
@media (max-width: 480px) {
  .result th:nth-child(3), .result td:nth-child(3) {
    min-width: 280px;
  }
}

.show--tb {
  display: none !important;
}
@media (max-width: 991px) {
  .show--tb {
    display: block !important;
  }
}

.hide--tb {
  display: block !important;
}
@media (max-width: 991px) {
  .hide--tb {
    display: none !important;
  }
}

.show--sm {
  display: none !important;
}
@media (max-width: 767px) {
  .show--sm {
    display: block !important;
  }
}

.hide--sm {
  display: block !important;
}
@media (max-width: 767px) {
  .hide--sm {
    display: none !important;
  }
}

.hide--mbxs {
  display: block !important;
}
@media (max-width: 374px) {
  .hide--mbxs {
    display: none !important;
  }
}

.table__mask {
  position: relative;
}
.table__mask-item {
  position: absolute;
  pointer-events: none;
  width: 12px;
  height: 12px;
  background: url("../images/table-mask.png");
  background-size: 100% auto;
  background-position: center top;
  background-repeat: no-repeat;
  z-index: 1;
}
.table__mask-item:first-child {
  top: 0;
  left: 0;
}
.table__mask-item:nth-child(2) {
  top: 0;
  right: 24px;
  -webkit-transform: rotateY(180deg);
          transform: rotateY(180deg);
}
.table__mask-item:nth-child(3) {
  left: 0;
  bottom: 24px;
  -webkit-transform: rotate(-90deg);
          transform: rotate(-90deg);
}
.table__mask-item:nth-child(4) {
  right: 24px;
  bottom: 24px;
  -webkit-transform: rotateY(180deg) rotate(-90deg);
          transform: rotateY(180deg) rotate(-90deg);
}

@-webkit-keyframes ani-s1-title-icon {
  0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  50% {
    -webkit-transform: translateY(-10px);
            transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}

@keyframes ani-s1-title-icon {
  0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  50% {
    -webkit-transform: translateY(-10px);
            transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
@-webkit-keyframes ani-btn {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    -webkit-transform: scale(0.95);
            transform: scale(0.95);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
@keyframes ani-btn {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    -webkit-transform: scale(0.95);
            transform: scale(0.95);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
@-webkit-keyframes ani-pop-img {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 -944px;
  }
}
@keyframes ani-pop-img {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 -944px;
  }
}
@-webkit-keyframes ani-pop-img-tb {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 -500px;
  }
}
@keyframes ani-pop-img-tb {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 -500px;
  }
}
@-webkit-keyframes ani-pop-img-mbxs {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 -400px;
  }
}
@keyframes ani-pop-img-mbxs {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 -400px;
  }
}