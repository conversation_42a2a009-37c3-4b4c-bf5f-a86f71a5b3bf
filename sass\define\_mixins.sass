// /*-- mixins --*/
//icon
// @mixin icon($img: 'common/icon',$p: 0px 0px,$size: 50px 50px)
//     background-size: $size
//     background-repeat: no-repeat
//     background-image: url($path+$img+".png")
//     background-position: $p

//hover-second
@mixin hover-second($s:.3s)
    transition-timing-function: ease
    transition-duration: $s
    
@mixin hover-second-o($s:.3s)
    transition: opacity $s ease

@mixin row($l: 1.24em,$r: 2)
    line-height: $l
    display: -webkit-box
    text-overflow : ellipsis
    -webkit-line-clamp: $r
    -webkit-box-orient: vertical
    overflow: hidden
    max-height: $l*$r