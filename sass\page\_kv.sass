// /*-- kv --*/
.kv
  $root: &
  position: relative
  overflow: hidden
  background: url($path+'bg_01.png'),url($path+'kv-bg.jpg')
  background-size: auto,cover
  background-position: center bottom,center top
  background-repeat: no-repeat,no-repeat
  @media (min-width:1922px)
    background-size: 100% auto,100% auto
  @media (max-width:$bk-lg)
    background-size: 1400px auto,1400px auto
  @media (max-width:$bk-md)
    background-size: 1200px auto,1200px auto
  @media (max-width:$bk-tb)
    background-size: 992px auto,992px auto
    margin-top: 40px
  @media (max-width:849px)
    background-size: 850px auto,850px auto
  @media (max-width:$bk-sm)
    background: url($path+'bg_01.png'),url($path+'kv-bg-sm.jpg')
    background-size: 100% auto,100% auto
    background-position: center bottom,center top
    background-repeat: no-repeat,no-repeat
    margin-top: 58px
  @media (max-width:$bk-mbxs)
    margin-top: 50px
  .container--lg
    padding-left: 35px
    height: 630px
    display: flex
    align-items: center
    padding-bottom: 70px
    @media (min-width:1922px)
      height: 32vw
    @media (max-width:$bk-xl)
      padding-left: 0
      padding-bottom: 50px
      height: 540px
    @media (max-width:$bk-lg)
      height: 420px
      padding-bottom: 10px
    @media (max-width:$bk-md)
      height: 380px
      padding-bottom: 0
    @media (max-width:$bk-tb)
      height: 320px
    @media (max-width:849px)
      height: 280px
    @media (max-width:$bk-sm)
      height: 106vw
      padding-left: 15px
      padding-top: 6vw
      align-items: flex-start
      justify-content: center
  &__item
    width: 10em
    font-size: $fontsize-64
    font-weight: $fontweight-bold
    color: #ffffff
    +row($l: 1.24em,$r: 2)
    // hyphens: manual
    // text-wrap: balance
    text-shadow: 0 0 14px rgba(196, 128, 8,0.56)
    @media (max-width:$bk-xl)
      font-size: $fontsize-54
      margin-left: -10px
    @media (max-width:$bk-lg)
      font-size: $fontsize-46
      margin-left: -60px
    @media (max-width:$bk-md)
      font-size: $fontsize-38
      position: absolute
      top: calc( 50% - 60px )
      left: calc( 50vw - 420px )
      text-shadow: 0 0 10px rgba(196, 128, 8,0.56)
    @media (max-width:$bk-tb)
      font-size: $fontsize-32
      top: calc( 50% - 55px )
      left: calc( 50vw - 350px )
    @media (max-width:849px)
      font-size: $fontsize-28
      top: calc( 50% - 50px )
      left: calc( 50vw - 290px )
    @media (max-width:$bk-sm)
      position: relative
      top: auto
      left: auto
      font-size: 9vw
      margin-left: auto
      margin-right: auto