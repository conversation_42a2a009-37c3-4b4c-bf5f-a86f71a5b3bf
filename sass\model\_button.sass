// /*-- button --*/

.btn
  color: #ffffff
  font-weight: $fontweight-medium
  font-size: $fontsize-primary
  letter-spacing: 0.05em
  text-align: center
  height: 48px
  border-radius: 8px
  border: none
  cursor: pointer
  +hover-second-o
  box-shadow: 0 0 7px rgba(141, 141, 141,0.38)
  display: inline-flex
  justify-content: center
  align-items: center
  @media (max-width:$bk-mblg)
    letter-spacing: 0
  @media (max-width:$bk-mbxs)
    font-size: $fontsize-14
    line-height: 40px
  @media (hover: hover)
    &:hover
      opacity: 0.8
  &:disabled,[disabled]
    background: $color-gray4
    cursor: not-allowed
    @media (hover: hover)
      &:hover
        opacity: 1
  &--lg
    width: 430px
    height: 80px
    font-size: $fontsize-22
    @media (max-width:$bk-mblg)
      width: 100%
      font-size: $fontsize-18
      height: 60px
  &--md
    width: 162px
    padding: 0
    @media (max-width:$bk-sm)
      width: 120px
    @media (max-width:$bk-mbxs)
      width: 90px
  &--md2
    width: 124px
    @media (max-width:$bk-sm)
      width: 5.5em
      
  &--md3
    width: 175px
    @media (max-width:$bk-sm)
      width: 6.5em
  &--sm
    width: 90px
    @media (max-width:$bk-sm)
      width: 74px
    @media (max-width:$bk-mbxs)
      width: 64px
  &--xs
    width: 36px
    height: 36px
    padding: 0
    @media (max-width:$bk-mbxs)
      width: 10vw
      height: 10vw
  &--green
    background: $color-green
  &--orange
    background: $color-orange
  &--gray
    background: $color-gray2
  &--excel
    font-size: $fontsize-14
    font-weight: $fontweight-medium
    color: $color-green
    letter-spacing: 0
    border: 1px solid $color-green
    background: transparent
    width: 118px
    height: 36px
    padding: 0
    @media (max-width:$bk-mbxs)
      width: 10vw
      height: 10vw
    [class^="icon"]
      margin-right: 6px
      @media (max-width:$bk-mbxs)
        margin-right: 0
  &--add
    font-size: $fontsize-primary
    font-weight: $fontweight-medium
    color: $color-orange
    letter-spacing: 0.05em
    border: 2px dotted $color-orange
    background: transparent
    width: 100%
    height: 55px
    [class^="icon"]
      margin-right: 8px
    @media (hover: hover)
      &:hover
        color: $color-white
        background: $color-orange
        [class^="icon"]
          &::before,&::after
            background: $color-white
            
