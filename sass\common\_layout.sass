// /*-- layout --*/
body
  font-family: $fontfamily-defult
  color: $color-gray
  font-weight: $fontweight-normal
  line-height: 1.2
  margin: 0 auto
  position: relative
  overflow-x: hidden
  background: url($path+'bg_02.jpg')
  background-size: auto
  background-position: center top
  background-repeat: repeat-y
  @media (min-width:1922px)
    background-size: 100% auto


select,button,textarea,input
  font-family: $fontfamily-defult
  outline: none

*
  -webkit-box-sizing: border-box
  -moz-box-sizing: border-box
  box-sizing: border-box
  -webkit-tap-highlight-color: transparent
  -webkit-text-size-adjust: none

// *:focus
//   outline: thin dotted $color-blue


table
  border-collapse: collapse

// a
//   color: $color-white
  // +hover-second-o

a,a:focus
  cursor: pointer
  text-decoration: none
  transition: color 300ms

// a:hover,
// a:focus
  // text-decoration: none

// a:hover
//   opacity: 0.8

a:active
  outline: none


// ::selection
//   color: $color-white
//   background: $color-brown


img
  -webkit-touch-callout: none
  -webkit-user-select: none
  -moz-user-select: none
  -ms-user-select: none
  user-select: none
  max-width: 100%


.container
  padding-right: 15px
  padding-left: 15px
  margin-right: auto
  margin-left: auto
  @media(min-width:768px)
    width: 650px
    padding-right: 0px
    padding-left: 0px
  @media(min-width:992px)
    width: 900px
  @media(min-width:1200px)
    width: 1050px
  @media(min-width:1400px)
    width: 1350px


.container--lg
  padding-right: 15px
  padding-left: 15px
  margin-right: auto
  margin-left: auto
  @media(min-width:768px)
    width: 650px
    padding-right: 0px
    padding-left: 0px
  @media(min-width:992px)
    width: 900px
  @media(min-width:1200px)
    width: 1050px
  @media(min-width:1400px)
    width: 1350px
  @media(min-width:1700px)
    width: 1680px