// /*-- common --*/
.show--tb
  display: none !important
  @media (max-width:$bk-tb)
    display: block !important
.hide--tb
  display: block !important
  @media (max-width:$bk-tb)
    display: none !important
.show--sm
  display: none !important
  @media (max-width:$bk-sm)
    display: block !important
.hide--sm
  display: block !important
  @media (max-width:$bk-sm)
    display: none !important
.hide--mbxs
  display: block !important
  @media (max-width:$bk-mbxs)
    display: none !important


  

.table
  &__mask
    position: relative
    &-item
      position: absolute
      pointer-events: none
      width: 12px
      height: 12px
      background: url($path+'table-mask.png')
      background-size: 100% auto
      background-position: center top
      background-repeat: no-repeat
      z-index: 1
      &:first-child
        top: 0
        left: 0
      &:nth-child(2)
        top: 0
        right: 24px
        transform: rotateY(180deg)
      &:nth-child(3)
        left: 0
        bottom: 24px
        transform: rotate(-90deg)
      &:nth-child(4)
        right: 24px
        bottom: 24px
        transform: rotateY(180deg) rotate(-90deg)