// /*-- header --*/
.header
  position: fixed
  top: 0
  left: 0
  z-index: 1020
  width: 100%
  background: #fff
  .container
    @media (min-width:$bk-min-sm)
      width: 100%
  &--active
    .header
      &__toggle
        @media (max-width:$bk-tb)
          z-index: 1040
          &-item
            &:first-child
              top: 14px
              transform: rotate(45deg)
            &:nth-child(2)
              display: none
            &:last-child
              top: 14px
              transform: rotate(-45deg)
  &__head
    @media (max-width:$bk-tb)
      display: inline-flex
      justify-content: space-between
      align-items: center
      width: 100%
  &__block
    display: inline-flex
    justify-content: space-between
    align-items: center
    width: 100%
    padding: 18px 45px 16px 38px
    @media (max-width:$bk-lg)
      padding-left: 15px
      padding-right: 15px
    @media (max-width:$bk-sm)
      padding: 5px 0
  &__logo
    display: block
    width: 194px
    @media (max-width:$bk-mblg)
      width: 130px
    @media (max-width:$bk-mbxs)
      width: 38vw
    img
      width: 100%
      max-width: 100%
      display: block
  &__btn
    display: inline-flex
    .btn
      margin-left: 20px
      @media (max-width:$bk-sm)
        margin-left: 10px
      @media (max-width:$bk-mbxs)
        margin-left: 5px
      &:first-child
        margin-left: 0