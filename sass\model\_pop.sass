// /*-- pop --*/
.pop
  $pop: &
  &__container
  &__wrap
    &--download
      position: relative
      z-index: 2
      width: 510px
      background: #ffffff
      border-radius: 30px
      padding: 35px 45px
      @media (max-width:$bk-sm)
        width: 90vw
      @media (max-width:$bk-mblg)
        padding-left: 15px
        padding-right: 15px
      #{$pop}
        &__body
          padding-top: 45px
          padding-bottom: 45px
        &__btn
          display: inline-flex
          width: 100%
          justify-content: space-between
          @media (max-width:$bk-sm)
            justify-content: center
            width: calc( 100% + 10px )
            margin-left: -5px
            margin-right: -5px
          @media (max-width:$bk-mbxs)
            flex-wrap: wrap
      .btn
        @media (max-width:$bk-sm)
          margin-left: 5px
          margin-right: 5px
          height: 66px
          line-height: 1.4
    &--setting
      position: relative
      z-index: 2
      width: 732px
      background: #ffffff
      border-radius: 30px
      padding: 35px 45px
      @media (max-width:$bk-sm)
        width: 90vw
      @media (max-width:$bk-mblg)
        padding-left: 15px
        padding-right: 15px
      #{$pop}
        &__body
          padding-top: 40px
        &__btn
          display: inline-flex
          justify-content: center
          width: calc( 100% + 20px )
          margin-left: -10px
          margin-right: -10px
          margin-top: 45px
          @media (max-width:$bk-sm)
            width: calc( 100% + 10px )
            margin-left: -5px
            margin-right: -5px
            margin-top: 30px
          @media (max-width:$bk-mbxs)
            flex-wrap: wrap
        &__form
          &-item
            display: inline-flex
            width: 100%
            padding-top: 26px
            padding-bottom: 26px
            border-top: 1px solid #d8d8d8
            &:first-child
              padding-top: 0
              border-top: none
            &:last-child
              padding-bottom: 0
      .btn
        margin-left: 10px
        margin-right: 10px
        @media (max-width:$bk-sm)
          margin-left: 5px
          margin-right: 5px
  &__title
    font-size: $fontsize-26
    font-weight: $fontweight-bold
    color: #383838
    letter-spacing: 0.05em
  &__lottery
    width: 502px
    height: 470px
    background: url($path+'pop-lottery.png')
    background-size: 100% auto
    background-position: center top
    background-repeat: no-repeat
    position: relative
    animation: ani-pop-img 1s steps(2) infinite
    @media (max-width:$bk-tb)
      width: 266px
      height: 250px
      animation-name: ani-pop-img-tb
    @media (max-width:$bk-mbxs)
      width: 213px
      height: 200px
      animation-name: ani-pop-img-mbxs